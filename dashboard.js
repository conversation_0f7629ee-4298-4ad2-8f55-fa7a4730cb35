// 仪表盘主逻辑
const searchInput = document.getElementById('search-input');
const quickLinksContainer = document.getElementById('quick-links');
const notesTextarea = document.getElementById('notes');

// 默认链接数据
const defaultLinks = [
  { name: 'Google', url: 'https://www.google.com' },
  { name: 'GitHub', url: 'https://github.com' },
  { name: 'Stack Overflow', url: 'https://stackoverflow.com' }
];

// 初始化仪表盘
function initDashboard() {
  // 加载保存的链接
  chrome.storage.sync.get(['quickLinks', 'notes'], (data) => {
    const links = data.quickLinks || defaultLinks;
    renderQuickLinks(links);
    
    // 加载笔记
    if (data.notes) {
      notesTextarea.value = data.notes;
    }
  });
  
  // 设置搜索功能
  searchInput.addEventListener('keypress', (e) => {
    if (e.key === 'Enter') {
      const query = searchInput.value.trim();
      if (query) {
        window.open(`https://www.google.com/search?q=${encodeURIComponent(query)}`, '_blank');
      }
    }
  });
  
  // 保存笔记
  notesTextarea.addEventListener('change', () => {
    chrome.storage.sync.set({ notes: notesTextarea.value });
  });
}

// 渲染快速链接
function renderQuickLinks(links) {
  quickLinksContainer.innerHTML = '';
  
  links.forEach(link => {
    const linkElement = document.createElement('a');
    linkElement.href = link.url;
    linkElement.textContent = link.name;
    linkElement.style.display = 'block';
    linkElement.style.margin = '5px 0';
    linkElement.style.color = '#4285f4';
    quickLinksContainer.appendChild(linkElement);
  });
}

// 启动仪表盘
initDashboard();