{"name": "my-vue-app", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc -b && vite build", "preview": "vite preview"}, "dependencies": {"@vueuse/core": "^13.3.0", "@vueuse/motion": "^3.0.3", "dexie": "^4.0.11", "echarts": "^5.6.0", "element-plus": "^2.10.5", "lucide-vue-next": "^0.512.0", "vue": "^3.5.13"}, "devDependencies": {"@inspira-ui/plugins": "^0.0.1", "@tailwindcss/vite": "^4.1.8", "@vitejs/plugin-vue": "^5.2.3", "@vue/tsconfig": "^0.7.0", "autoprefixer": "^10.4.21", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "postcss": "^8.5.4", "tailwind-merge": "^3.3.0", "tailwindcss": "^4.1.8", "tailwindcss-animate": "^1.0.7", "typescript": "~5.8.3", "unplugin-auto-import": "^19.3.0", "vite": "^6.3.5", "vue-tsc": "^2.2.8"}}