import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'

import path from 'path'
import tailwindcss from '@tailwindcss/vite';

// 引入依赖
import AutoImport from "unplugin-auto-import/vite";

// https://vite.dev/config/
export default defineConfig({
  plugins: [
    vue(),
    tailwindcss(),
    AutoImport({
      include: [
        /\.[tj]sx?$/, // .ts, .tsx, .js, .jsx
        /\.vue$/,
        /\.vue\?vue/, // .vue
      ],
      eslintrc: {
        enabled: true,
      },
      imports: ["vue", "vue-router"],
    }),
  ],
  resolve: {
    extensions: ['.vue', '.ts'],
    alias: {
      "@": path.resolve(__dirname, "src")
    }
  },
  build: {
    outDir: './dist',
    rollupOptions: {
      input: {
        dashboard: './dashboard.html'
      },
      output: {
        entryFileNames: `assets/[name].js`,
        chunkFileNames: `assets/[name].js`,
        assetFileNames: `assets/[name].[ext]`
      }
    },
    emptyOutDir: false,
    copyPublicDir: false
  },
})
