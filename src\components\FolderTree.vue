<!-- components/FolderTree.vue -->
<template>
  <div class="folder-tree" :style="{ marginLeft: `${folder.level * 20}px` }">
    <!-- 文件夹标题 -->
    <div class="folder-header" @click="toggleExpand" :class="{ 'has-children': folder.children.length > 0 }">
      <div class="folder-title-wrapper">
        <!-- 展开/折叠图标 -->
        <div v-if="folder.children.length > 0" class="expand-icon" :class="{ expanded: folder.expanded }">
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <polyline points="9 18 15 12 9 6"></polyline>
          </svg>
        </div>
        
        <!-- 文件夹图标 -->
        <div class="folder-icon">
          <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <path d="M22 19a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h5l2 3h9a2 2 0 0 1 2 2z"></path>
          </svg>
        </div>
        
        <!-- 文件夹名称 -->
        <h3 class="folder-title">{{ folder.name }}</h3>
      </div>
      
      <!-- 统计信息 -->
      <div class="folder-stats">
        <span v-if="folder.bookmarks.length > 0" class="bookmark-count">{{ folder.bookmarks.length }} 书签</span>
        <span v-if="folder.children.length > 0" class="child-count">{{ folder.children.length }} 子分类</span>
      </div>
    </div>

    <!-- 内容区域（书签和子文件夹） -->
    <div class="folder-content" v-show="folder.expanded || searchQuery">
      <!-- 书签列表 -->
      <div v-if="folder.bookmarks.length > 0" class="bookmarks-grid">
        <div 
          v-for="(bookmark, index) in folder.bookmarks" 
          :key="index"
          class="bookmark-card"
          @click="$emit('bookmark-click', bookmark)"
          :title="`${bookmark.title}\n${bookmark.url}`"
        >
          <div class="card-content">
            <!-- 图标区域 -->
            <div class="icon-wrapper" :class="{ 'no-icon': !bookmark.favicon }">
              <img 
                v-if="bookmark.favicon" 
                :src="bookmark.favicon" 
                :alt="bookmark.title"
                @error="useDefaultIcon"
                class="favicon"
                loading="lazy"
              />
              <div v-else class="default-icon">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <circle cx="12" cy="12" r="10"></circle>
                  <line x1="2" y1="12" x2="22" y2="12"></line>
                  <path d="M12 2a15.3 15.3 0 0 1 4 10 15.3 15.3 0 0 1-4 10 15.3 15.3 0 0 1-4-10 15.3 15.3 0 0 1 4-10z"></path>
                </svg>
              </div>
            </div>

            <!-- 文本内容 -->
            <div class="text-content">
              <h4 class="bookmark-title">{{ bookmark.title }}</h4>
              <p class="bookmark-url">{{ bookmark.hostname }}</p>
            </div>
          </div>
        </div>
      </div>

      <!-- 子文件夹 -->
      <div v-if="folder.children.length > 0" class="sub-folders">
        <FolderTree 
          v-for="(child, index) in folder.children" 
          :key="index"
          :folder="child"
          :search-query="searchQuery"
          @bookmark-click="$emit('bookmark-click', $event)"
        />
      </div>
    </div>
  </div>
</template>

<script setup>
import { defineProps, defineEmits } from 'vue'

const props = defineProps({
  folder: {
    type: Object,
    required: true
  },
  searchQuery: {
    type: String,
    default: ''
  }
})

const emits = defineEmits(['bookmark-click'])

// 切换展开/折叠状态
const toggleExpand = () => {
  if (props.folder.children.length > 0) {
    props.folder.expanded = !props.folder.expanded
  }
}

// 图标加载失败
const useDefaultIcon = (event) => {
  const img = event.target
  img.style.display = 'none'
}
</script>

<style scoped>
.folder-tree {
  margin-bottom: 8px;
  transition: all 0.3s ease;
}

/* 文件夹标题样式 */
.folder-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: #f8fafc;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
  border: 1px solid #e2e8f0;
}

.folder-header:hover {
  background: #f1f5f9;
  border-color: #6b72e8;
}

.folder-header.has-children {
  border-bottom-left-radius: 0;
  border-bottom-right-radius: 0;
  border-bottom: none;
}

/* 标题内容 */
.folder-title-wrapper {
  display: flex;
  align-items: center;
  gap: 10px;
  flex: 1;
}

/* 展开/折叠图标 */
.expand-icon {
  flex-shrink: 0;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #64748b;
  transition: transform 0.2s ease;
}

.expand-icon.expanded {
  transform: rotate(90deg);
}

/* 文件夹图标 */
.folder-icon {
  flex-shrink: 0;
  color: #6b72e8;
}

/* 文件夹名称 */
.folder-title {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #1e293b;
  line-height: 1.4;
}

/* 统计信息 */
.folder-stats {
  display: flex;
  gap: 12px;
  margin-left: 12px;
  font-size: 13px;
  color: #64748b;
  white-space: nowrap;
}

.bookmark-count, .child-count {
  background: #e2e8f0;
  padding: 4px 8px;
  border-radius: 8px;
}

/* 内容区域 */
.folder-content {
  border: 1px solid #e2e8f0;
  border-top: none;
  border-radius: 0 0 12px 12px;
  overflow: hidden;
}

/* 书签网格 */
.bookmarks-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
  gap: 12px;
  padding: 16px;
  background: white;
}

/* 书签卡片 */
.bookmark-card {
  background: #f8fafc;
  border: 2px solid #e2e8f0;
  border-radius: 12px;
  padding: 12px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  position: relative;
  overflow: hidden;
  height: 80px;
}

.bookmark-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border-color: #6b72e8;
}

.card-content {
  display: flex;
  align-items: flex-start;
  gap: 10px;
  height: 100%;
}

/* 图标样式 */
.icon-wrapper {
  flex-shrink: 0;
  width: 32px;
  height: 32px;
  border-radius: 8px;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  background: white;
  border: 1px solid #e2e8f0;
  transition: all 0.2s ease;
}

.bookmark-card:hover .icon-wrapper {
  background: #6b72e8;
  border-color: #6b72e8;
}

.icon-wrapper.no-icon {
  background: #f1f5f9;
  border-color: #e2e8f0;
}

.favicon {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: opacity 0.2s ease;
}

.default-icon {
  width: 18px;
  height: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.default-icon svg {
  width: 100%;
  height: 100%;
  stroke: #64748b;
  transition: all 0.2s ease;
}

.bookmark-card:hover .default-icon svg {
  stroke: white;
}

/* 文本内容 */
.text-content {
  flex: 1;
  min-width: 0;
}

.bookmark-title {
  margin: 0 0 2px;
  font-size: 14px;
  font-weight: 600;
  color: #1e293b;
  line-height: 1.3;
  max-height: 2.6em;
  overflow: hidden;
  text-overflow: ellipsis;
}

.bookmark-url {
  margin: 0;
  font-size: 12px;
  color: #64748b;
  font-weight: 500;
  max-height: 1.6em;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 子文件夹 */
.sub-folders {
  padding: 0 0 8px;
  border-top: 1px dashed #e2e8f0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .folder-title {
    font-size: 15px;
  }
  
  .folder-stats {
    font-size: 12px;
  }
  
  .bookmarks-grid {
    grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
    gap: 10px;
    padding: 12px;
  }
  
  .bookmark-card {
    padding: 10px;
    height: 75px;
  }
  
  .icon-wrapper {
    width: 30px;
    height: 30px;
  }
}

@media (max-width: 480px) {
  .bookmarks-grid {
    grid-template-columns: 1fr;
  }
  
  .folder-stats {
    flex-direction: column;
    gap: 4px;
  }
}
</style>