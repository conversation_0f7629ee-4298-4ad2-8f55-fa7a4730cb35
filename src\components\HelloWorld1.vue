<template>
  <div class="diet-tracker">
    <!-- Animated background with particles -->
    <div class="particles-container">
      <div v-for="i in 20" :key="i" class="particle"></div>
    </div>

    <div class="container">
      <!-- Header with 3D text effect -->
      <div class="header">
        <div class="title-container">
          <h1 class="title">饮食运动记录表</h1>
          <div class="title-shadow"></div>
        </div>
        <p class="subtitle">原则：低糖、低脂、高蛋白、高纤维</p>
      </div>
      
      <!-- Main table with glass morphism effect -->
      <div class="table-container">
        <div class="controls">
          <div class="search-box">
            <input type="text" placeholder="搜索记录..." />
            <div class="search-icon">
              <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <circle cx="11" cy="11" r="8"></circle>
                <line x1="21" y1="21" x2="16.65" y2="16.65"></line>
              </svg>
            </div>
          </div>
          <div class="filter-button">
            <span>筛选</span>
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <polygon points="22 3 2 3 10 12.46 10 19 14 21 14 12.46 22 3"></polygon>
            </svg>
          </div>
        </div>
        
        <div class="table-wrapper">
          <table>
            <thead>
              <tr>
                <th>日期</th>
                <th>怎么执行的</th>
                <th>是否运动</th>
                <th>体重</th>
                <th>备注</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="(row, index) in tableData" :key="index" class="data-row" :class="{'highlight': index === activeRow}">
                <td class="date-cell" @click="activeRow = index">
                  <div class="date-content">
                    <span class="date">{{ row.date }}</span>
                    <span class="day">{{ row.day }}</span>
                  </div>
                </td>
                <td class="meals-cell">
                  <div class="meals-container">
                    <div v-for="meal in row.meals" :key="meal.type" class="meal-item" :class="getMealClass(meal.type)">
                      <div class="meal-type">{{ meal.type }}</div>
                      <div class="meal-food">{{ meal.food || '未记录' }}</div>
                    </div>
                  </div>
                </td>
                <td class="exercise-cell">
                  <div v-if="row.exercise" class="exercise-badge">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                      <path d="M18 8h1a4 4 0 0 1 0 8h-1"></path>
                      <path d="M2 8h16v9a4 4 0 0 1-4 4H6a4 4 0 0 1-4-4V8z"></path>
                      <line x1="6" y1="1" x2="6" y2="4"></line>
                      <line x1="10" y1="1" x2="10" y2="4"></line>
                      <line x1="14" y1="1" x2="14" y2="4"></line>
                    </svg>
                    {{ row.exercise }}
                  </div>
                </td>
                <td class="weight-cell">
                  <div v-if="row.weight" class="weight-badge">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                      <path d="M16 18 22 12 16 6"></path>
                      <path d="M8 6 2 12 8 18"></path>
                    </svg>
                    {{ row.weight }}
                  </div>
                </td>
                <td class="notes-cell">
                  <div class="notes-content">
                    <div class="notes-text">{{ row.notes }}</div>
                    <div v-if="row.notes" class="notes-expand" @click="expandNotes(index)">
                      <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <polyline points="6 9 12 15 18 9"></polyline>
                      </svg>
                    </div>
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
      
      <!-- Stats cards with hover effects -->
      <div class="stats-container">
        <div class="stat-card exercise">
          <div class="stat-icon">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path d="M18.6 18.6L12 12V2.5"></path>
              <circle cx="12" cy="12" r="10"></circle>
            </svg>
          </div>
          <div class="stat-content">
            <h3>运动记录</h3>
            <p>坚持锻炼，保持健康</p>
            <div class="stat-progress">
              <div class="progress-bar" style="width: 65%"></div>
            </div>
          </div>
        </div>
        
        <div class="stat-card diet">
          <div class="stat-icon">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path d="M12 2v8"></path>
              <path d="m4.93 10.93 1.41 1.41"></path>
              <path d="M2 18h2"></path>
              <path d="M20 18h2"></path>
              <path d="m19.07 10.93-1.41 1.41"></path>
              <path d="M22 22H2"></path>
              <path d="M16 6 7 22"></path>
              <path d="m8 6 9 16"></path>
            </svg>
          </div>
          <div class="stat-content">
            <h3>饮食管理</h3>
            <p>合理搭配，营养均衡</p>
            <div class="stat-progress">
              <div class="progress-bar" style="width: 80%"></div>
            </div>
          </div>
        </div>
        
        <div class="stat-card weight">
          <div class="stat-icon">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <line x1="12" y1="20" x2="12" y2="10"></line>
              <line x1="18" y1="20" x2="18" y2="4"></line>
              <line x1="6" y1="20" x2="6" y2="16"></line>
            </svg>
          </div>
          <div class="stat-content">
            <h3>体重监控</h3>
            <p>定期记录，跟踪变化</p>
            <div class="stat-progress">
              <div class="progress-bar" style="width: 45%"></div>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- Modal for expanded notes -->
    <div v-if="showModal" class="modal" @click="closeModal">
      <div class="modal-content" @click.stop>
        <div class="modal-header">
          <h3>详细备注</h3>
          <button class="close-button" @click="closeModal">×</button>
        </div>
        <div class="modal-body">
          <p>{{ selectedNote }}</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'

const tableData = ref([
  {
    date: '6.4',
    day: '三',
    meals: [
      { type: '早餐', food: '' },
      { type: '午饭', food: '' },
      { type: '晚上', food: '鸡蛋火腿三明治，加餐（可选）无糖希腊酸奶100g + 小番茄10颗' }
    ],
    exercise: '是',
    weight: '',
    notes: '6:20：起床喝温水，6分钟拉伸。6:35：洗漱。7:00：出门送老婆。7:40：快走30分钟。11:50：午餐，饭后散步20-40分钟。18:30：晚餐，饭后站立30分钟。20:00：运动（按计划）。23:00前：入睡。'
  },
  {
    date: '6.5',
    day: '四',
    meals: [
      { type: '早餐', food: '红薯、鸡蛋2' },
      { type: '午饭', food: '跟随公司（主食≤1拳头）蛋白质每餐≥1手掌（100-150g），蔬菜不限量但少油。' },
      { type: '晚上', food: '豆腐200g（清蒸） + 蒸南瓜150g + 水煮菠菜200g' }
    ],
    exercise: '',
    weight: '',
    notes: '戒除：含糖饮料（包括果汁）、酒精、油炸食品、甜点、白面包/白米饭（替换为粗粮）'
  },
  {
    date: '6.6',
    day: '五',
    meals: [
      { type: '早餐', food: '玉米、鸡蛋1' },
      { type: '午饭', food: '跟随公司（主食≤1拳头）蛋白质每餐≥1手掌（100-150g），蔬菜不限量但少油。' },
      { type: '晚上', food: '全麦面包、去皮鸡腿、凉拌金针菇' }
    ],
    exercise: '',
    weight: '',
    notes: '每天额外步行≥6000步（如通勤、散步）运动前后补充水分，避免空腹运动（防低血糖）。'
  },
  {
    date: '6.7',
    day: '六',
    meals: [
      { type: '早餐', food: '全麦面包、橙子' },
      { type: '午饭', food: '要是出去溜达就在外面吃，咨询老婆合理搭配' },
      { type: '晚上', food: '荞麦面、水煮虾、凉拌娃娃菜' }
    ],
    exercise: '',
    weight: '',
    notes: '每天早晨起床喝200-300ml温水40-50℃，刷牙后、早餐前饮用柠檬片1片（维生素C促进代谢，胃酸过多者慎用）。生姜2薄片（驱寒暖胃，适合体寒者）。喝完温水后做5分钟拉伸（如猫式伸展），进一步激活代谢'
  },
  {
    date: '6.8',
    day: '日',
    meals: [
      { type: '早餐', food: '五谷杂粮粥、苹果' },
      { type: '午饭', food: '要是出去溜达就在外面吃，咨询老婆合理搭配' },
      { type: '晚上', food: '一个小紫薯、番茄炒鸡蛋、10颗圣女果' }
    ],
    exercise: '',
    weight: '',
    notes: ''
  },
  {
    date: '7.21',
    day: '六',
    meals: [
      { type: '早餐', food: '' },
      { type: '午饭', food: '' },
      { type: '晚上', food: '' }
    ],
    exercise: '',
    weight: '',
    notes: '会议，适当适宜以来大家'
  },
  {
    date: '7.22',
    day: '日',
    meals: [
      { type: '早餐', food: '' },
      { type: '午饭', food: '' },
      { type: '晚上', food: '' }
    ],
    exercise: '',
    weight: '',
    notes: '可能不会度，或只是不会'
  }
])

// Interactive state
const activeRow = ref(null)
const showModal = ref(false)
const selectedNote = ref('')

// Methods
const getMealClass = (type) => {
  switch (type) {
    case '早餐':
      return 'breakfast'
    case '午饭':
      return 'lunch'
    case '晚上':
      return 'dinner'
    default:
      return ''
  }
}

const expandNotes = (index) => {
  selectedNote.value = tableData.value[index].notes
  showModal.value = true
}

const closeModal = () => {
  showModal.value = false
}
</script>

<style scoped>
/* Base styles */
.diet-tracker {
  min-height: 100vh;
  background: linear-gradient(135deg, #0f0c29, #302b63, #24243e);
  position: relative;
  overflow: hidden;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
  color: #fff;
  padding: 2rem 1rem;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  position: relative;
  z-index: 10;
}

/* Animated particles */
.particles-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
  z-index: 1;
}

.particle {
  position: absolute;
  width: 6px;
  height: 6px;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  animation: float 15s infinite linear;
  opacity: 0;
}

@keyframes float {
  0% {
    transform: translateY(100vh) translateX(0) scale(0);
    opacity: 0;
  }
  10% {
    opacity: 1;
  }
  90% {
    opacity: 1;
  }
  100% {
    transform: translateY(-100px) translateX(100px) scale(1);
    opacity: 0;
  }
}

/* Generate random positions and delays for particles */
.particles-container .particle:nth-child(1) { left: 10%; animation-delay: 0s; }
.particles-container .particle:nth-child(2) { left: 20%; animation-delay: 2s; }
.particles-container .particle:nth-child(3) { left: 30%; animation-delay: 4s; }
.particles-container .particle:nth-child(4) { left: 40%; animation-delay: 6s; }
.particles-container .particle:nth-child(5) { left: 50%; animation-delay: 8s; }
.particles-container .particle:nth-child(6) { left: 60%; animation-delay: 10s; }
.particles-container .particle:nth-child(7) { left: 70%; animation-delay: 12s; }
.particles-container .particle:nth-child(8) { left: 80%; animation-delay: 14s; }
.particles-container .particle:nth-child(9) { left: 90%; animation-delay: 16s; }
.particles-container .particle:nth-child(10) { left: 5%; animation-delay: 1s; }
.particles-container .particle:nth-child(11) { left: 15%; animation-delay: 3s; }
.particles-container .particle:nth-child(12) { left: 25%; animation-delay: 5s; }
.particles-container .particle:nth-child(13) { left: 35%; animation-delay: 7s; }
.particles-container .particle:nth-child(14) { left: 45%; animation-delay: 9s; }
.particles-container .particle:nth-child(15) { left: 55%; animation-delay: 11s; }
.particles-container .particle:nth-child(16) { left: 65%; animation-delay: 13s; }
.particles-container .particle:nth-child(17) { left: 75%; animation-delay: 15s; }
.particles-container .particle:nth-child(18) { left: 85%; animation-delay: 17s; }
.particles-container .particle:nth-child(19) { left: 95%; animation-delay: 19s; }
.particles-container .particle:nth-child(20) { left: 99%; animation-delay: 21s; }

/* Header with 3D text effect */
.header {
  text-align: center;
  margin-bottom: 3rem;
  position: relative;
}

.title-container {
  position: relative;
  display: inline-block;
  perspective: 1000px;
  margin-bottom: 1rem;
}

.title {
  font-size: 3rem;
  font-weight: 800;
  background: linear-gradient(to right, #ff7e5f, #feb47b, #ffcb80, #00c9ff);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
  background-size: 300% 300%;
  animation: gradient 8s ease infinite;
  text-shadow: 0 0 10px rgba(255, 255, 255, 0.2);
  letter-spacing: 2px;
  transform-style: preserve-3d;
  transform: rotateX(10deg);
  transition: transform 0.3s ease;
}

.title-container:hover .title {
  transform: rotateX(15deg) scale(1.05);
}

.title-shadow {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  height: 20px;
  background: radial-gradient(ellipse at center, rgba(0, 201, 255, 0.3) 0%, rgba(0, 0, 0, 0) 70%);
  transform: rotateX(90deg) translateZ(-10px);
  filter: blur(5px);
  opacity: 0.7;
}

.subtitle {
  font-size: 1.2rem;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 2rem;
  text-shadow: 0 0 10px rgba(0, 0, 0, 0.5);
  letter-spacing: 1px;
}

/* Table container with glass morphism */
.table-container {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2), 
              inset 0 0 0 1px rgba(255, 255, 255, 0.1);
  overflow: hidden;
  margin-bottom: 2rem;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.table-container:hover {
  transform: translateY(-5px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.3), 
              inset 0 0 0 1px rgba(255, 255, 255, 0.2);
}

/* Controls section */
.controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.search-box {
  position: relative;
  width: 300px;
}

.search-box input {
  width: 100%;
  padding: 0.75rem 1rem 0.75rem 2.5rem;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 50px;
  color: white;
  font-size: 0.9rem;
  transition: all 0.3s ease;
}

.search-box input:focus {
  outline: none;
  background: rgba(255, 255, 255, 0.15);
  border-color: rgba(255, 255, 255, 0.3);
  box-shadow: 0 0 0 3px rgba(255, 255, 255, 0.1);
}

.search-icon {
  position: absolute;
  left: 0.75rem;
  top: 50%;
  transform: translateY(-50%);
  color: rgba(255, 255, 255, 0.6);
}

.filter-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.25rem;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 50px;
  color: white;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.filter-button:hover {
  background: rgba(255, 255, 255, 0.2);
}

/* Table styles */
.table-wrapper {
  overflow-x: auto;
  padding: 0.5rem;
}

table {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0 8px;
}

thead th {
  padding: 1rem;
  text-align: left;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.8);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  position: sticky;
  top: 0;
  background: rgba(48, 43, 99, 0.9);
  backdrop-filter: blur(10px);
  z-index: 10;
}

.data-row {
  background: rgba(255, 255, 255, 0.05);
  transition: all 0.3s ease;
  border-radius: 12px;
}

.data-row td {
  padding: 1rem;
  border-top: 1px solid rgba(255, 255, 255, 0.05);
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}

.data-row td:first-child {
  border-top-left-radius: 12px;
  border-bottom-left-radius: 12px;
  border-left: 1px solid rgba(255, 255, 255, 0.05);
}

.data-row td:last-child {
  border-top-right-radius: 12px;
  border-bottom-right-radius: 12px;
  border-right: 1px solid rgba(255, 255, 255, 0.05);
}

.data-row:hover {
  background: rgba(255, 255, 255, 0.1);
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.data-row.highlight {
  background: rgba(0, 201, 255, 0.1);
  border: 1px solid rgba(0, 201, 255, 0.3);
}

/* Date cell */
.date-cell {
  width: 100px;
  cursor: pointer;
}

.date-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  padding: 0.75rem;
  transition: all 0.3s ease;
}

.date-cell:hover .date-content {
  background: rgba(255, 255, 255, 0.2);
  transform: scale(1.05);
}

.date {
  font-size: 1.2rem;
  font-weight: 700;
  color: white;
}

.day {
  font-size: 0.8rem;
  color: rgba(255, 255, 255, 0.7);
  margin-top: 0.25rem;
}

/* Meals cell */
.meals-cell {
  width: 35%;
}

.meals-container {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.meal-item {
  display: flex;
  align-items: center;
  padding: 0.5rem;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.meal-item:hover {
  transform: translateX(5px);
}

.meal-item.breakfast {
  background: rgba(255, 193, 7, 0.1);
  border-left: 3px solid rgba(255, 193, 7, 0.5);
}

.meal-item.lunch {
  background: rgba(255, 87, 34, 0.1);
  border-left: 3px solid rgba(255, 87, 34, 0.5);
}

.meal-item.dinner {
  background: rgba(103, 58, 183, 0.1);
  border-left: 3px solid rgba(103, 58, 183, 0.5);
}

.meal-type {
  font-weight: 600;
  font-size: 0.85rem;
  padding: 0.25rem 0.75rem;
  border-radius: 50px;
  margin-right: 0.75rem;
  min-width: 60px;
  text-align: center;
}

.breakfast .meal-type {
  background: rgba(255, 193, 7, 0.2);
  color: rgba(255, 193, 7, 0.9);
}

.lunch .meal-type {
  background: rgba(255, 87, 34, 0.2);
  color: rgba(255, 87, 34, 0.9);
}

.dinner .meal-type {
  background: rgba(103, 58, 183, 0.2);
  color: rgba(103, 58, 183, 0.9);
}

.meal-food {
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.9);
  flex: 1;
}

/* Exercise cell */
.exercise-cell {
  width: 100px;
  text-align: center;
}

.exercise-badge {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background: rgba(76, 175, 80, 0.2);
  border: 1px solid rgba(76, 175, 80, 0.3);
  border-radius: 50px;
  color: rgba(76, 175, 80, 0.9);
  font-weight: 600;
  transition: all 0.3s ease;
}

.exercise-badge:hover {
  background: rgba(76, 175, 80, 0.3);
  transform: scale(1.05);
}

/* Weight cell */
.weight-cell {
  width: 100px;
  text-align: center;
}

.weight-badge {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background: linear-gradient(135deg, rgba(33, 150, 243, 0.2), rgba(156, 39, 176, 0.2));
  border: 1px solid rgba(33, 150, 243, 0.3);
  border-radius: 50px;
  color: white;
  font-weight: 600;
  transition: all 0.3s ease;
}

.weight-badge:hover {
  transform: scale(1.05);
  box-shadow: 0 0 15px rgba(33, 150, 243, 0.3);
}

/* Notes cell */
.notes-cell {
  width: 25%;
}

.notes-content {
  position: relative;
  max-height: 80px;
  overflow: hidden;
  padding-right: 20px;
}

.notes-text {
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.5;
}

.notes-expand {
  position: absolute;
  bottom: 0;
  right: 0;
  background: linear-gradient(to left, rgba(48, 43, 99, 0.9) 0%, rgba(48, 43, 99, 0) 100%);
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.notes-expand:hover {
  transform: translateY(-2px);
}

/* Stats cards */
.stats-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
  margin-top: 2rem;
}

.stat-card {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  padding: 1.5rem;
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0) 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-5px);
}

.stat-card:hover::before {
  opacity: 1;
}

.stat-icon {
  width: 50px;
  height: 50px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.exercise .stat-icon {
  background: rgba(33, 150, 243, 0.2);
  color: rgba(33, 150, 243, 0.9);
}

.diet .stat-icon {
  background: rgba(76, 175, 80, 0.2);
  color: rgba(76, 175, 80, 0.9);
}

.weight .stat-icon {
  background: rgba(156, 39, 176, 0.2);
  color: rgba(156, 39, 176, 0.9);
}

.stat-card:hover .stat-icon {
  transform: scale(1.1) rotate(5deg);
}

.stat-content {
  flex: 1;
}

.stat-content h3 {
  font-size: 1.2rem;
  font-weight: 600;
  margin-bottom: 0.25rem;
}

.stat-content p {
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.7);
  margin-bottom: 1rem;
}

.stat-progress {
  height: 6px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
  overflow: hidden;
}

.progress-bar {
  height: 100%;
  border-radius: 3px;
  transition: width 1s ease;
}

.exercise .progress-bar {
  background: linear-gradient(to right, #2196f3, #03a9f4);
}

.diet .progress-bar {
  background: linear-gradient(to right, #4caf50, #8bc34a);
}

.weight .progress-bar {
  background: linear-gradient(to right, #9c27b0, #673ab7);
}

/* Modal */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(5px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 100;
  animation: fadeIn 0.3s ease;
}

.modal-content {
  background: linear-gradient(135deg, #302b63, #24243e);
  border-radius: 16px;
  width: 90%;
  max-width: 600px;
  max-height: 80vh;
  overflow-y: auto;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.3);
  animation: scaleIn 0.3s ease;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.modal-header h3 {
  font-size: 1.5rem;
  font-weight: 600;
}

.close-button {
  background: none;
  border: none;
  color: rgba(255, 255, 255, 0.7);
  font-size: 1.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.close-button:hover {
  color: white;
  transform: rotate(90deg);
}

.modal-body {
  padding: 1.5rem;
}

.modal-body p {
  line-height: 1.6;
  color: rgba(255, 255, 255, 0.9);
}

/* Animations */
@keyframes gradient {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes scaleIn {
  from {
    transform: scale(0.9);
    opacity: 0;
  }
  to {
    transform: scale(1);
    opacity: 1;
  }
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .title {
    font-size: 2rem;
  }
  
  .controls {
    flex-direction: column;
    gap: 1rem;
  }
  
  .search-box {
    width: 100%;
  }
  
  .stats-container {
    grid-template-columns: 1fr;
  }
  
  .date-cell, .exercise-cell, .weight-cell {
    width: auto;
  }
  
  .meals-cell {
    width: auto;
  }
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.2);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.3);
}
</style>