/**
 * Dexie.js 数据库操作测试文件
 * 用于验证用户管理系统的数据库功能
 */

import { userDB } from '../lib/db.js';

/**
 * 测试数据库基本操作
 */
async function testDatabaseOperations() {
  console.log('开始测试 Dexie.js 数据库操作...');
  
  try {
    // 1. 清空数据库（测试环境）
    console.log('1. 清空测试数据...');
    await userDB.clearAllUsers();
    
    // 2. 测试添加用户
    console.log('2. 测试添加用户...');
    const newUser = {
      name: "测试用户",
      email: "<EMAIL>",
      department: "测试部",
      position: "测试工程师",
      status: "在职",
      joinDate: "2024-01-01"
    };
    
    const userId = await userDB.addUser(newUser);
    console.log('添加用户成功，ID:', userId);
    
    // 3. 测试获取所有用户
    console.log('3. 测试获取所有用户...');
    const allUsers = await userDB.getAllUsers();
    console.log('所有用户:', allUsers);
    
    // 4. 测试根据ID获取用户
    console.log('4. 测试根据ID获取用户...');
    const user = await userDB.getUserById(userId);
    console.log('获取的用户:', user);
    
    // 5. 测试更新用户
    console.log('5. 测试更新用户...');
    await userDB.updateUser(userId, {
      name: "更新后的测试用户",
      position: "高级测试工程师"
    });
    
    const updatedUser = await userDB.getUserById(userId);
    console.log('更新后的用户:', updatedUser);
    
    // 6. 测试搜索用户
    console.log('6. 测试搜索用户...');
    const searchResults = await userDB.searchUsers('测试');
    console.log('搜索结果:', searchResults);
    
    // 7. 测试批量添加用户
    console.log('7. 测试批量添加用户...');
    const batchUsers = [
      {
        name: "批量用户1",
        email: "<EMAIL>",
        department: "技术部",
        position: "开发工程师",
        status: "在职",
        joinDate: "2024-01-02"
      },
      {
        name: "批量用户2",
        email: "<EMAIL>",
        department: "产品部",
        position: "产品经理",
        status: "在职",
        joinDate: "2024-01-03"
      }
    ];
    
    await userDB.bulkAddUsers(batchUsers);
    console.log('批量添加用户成功');
    
    // 8. 测试获取用户总数
    console.log('8. 测试获取用户总数...');
    const userCount = await userDB.getUserCount();
    console.log('用户总数:', userCount);
    
    // 9. 测试分页获取用户
    console.log('9. 测试分页获取用户...');
    const paginatedUsers = await userDB.getUsersPaginated(1, 2);
    console.log('分页用户 (第1页，每页2条):', paginatedUsers);
    
    // 10. 测试删除用户
    console.log('10. 测试删除用户...');
    await userDB.deleteUser(userId);
    console.log('删除用户成功');
    
    // 验证删除结果
    const finalUsers = await userDB.getAllUsers();
    console.log('删除后的所有用户:', finalUsers);
    
    console.log('✅ 所有数据库操作测试通过！');
    
  } catch (error) {
    console.error('❌ 数据库操作测试失败:', error);
  }
}

/**
 * 测试错误处理
 */
async function testErrorHandling() {
  console.log('\n开始测试错误处理...');
  
  try {
    // 测试获取不存在的用户
    console.log('1. 测试获取不存在的用户...');
    const nonExistentUser = await userDB.getUserById(99999);
    console.log('不存在的用户结果:', nonExistentUser); // 应该返回 undefined
    
    // 测试删除不存在的用户
    console.log('2. 测试删除不存在的用户...');
    const deleteResult = await userDB.deleteUser(99999);
    console.log('删除不存在用户的结果:', deleteResult); // 应该返回 0
    
    console.log('✅ 错误处理测试通过！');
    
  } catch (error) {
    console.error('❌ 错误处理测试失败:', error);
  }
}

/**
 * 性能测试
 */
async function testPerformance() {
  console.log('\n开始性能测试...');
  
  try {
    // 清空数据库
    await userDB.clearAllUsers();
    
    // 生成大量测试数据
    console.log('生成1000条测试数据...');
    const startTime = Date.now();
    
    const largeDataSet = [];
    for (let i = 1; i <= 1000; i++) {
      largeDataSet.push({
        name: `用户${i}`,
        email: `user${i}@example.com`,
        department: `部门${i % 5 + 1}`,
        position: `职位${i % 10 + 1}`,
        status: i % 10 === 0 ? "离职" : "在职",
        joinDate: `2024-01-${String(i % 28 + 1).padStart(2, '0')}`
      });
    }
    
    // 批量插入
    await userDB.bulkAddUsers(largeDataSet);
    const insertTime = Date.now() - startTime;
    console.log(`批量插入1000条数据耗时: ${insertTime}ms`);
    
    // 测试查询性能
    const queryStartTime = Date.now();
    const allUsers = await userDB.getAllUsers();
    const queryTime = Date.now() - queryStartTime;
    console.log(`查询所有数据耗时: ${queryTime}ms，数据量: ${allUsers.length}`);
    
    // 测试搜索性能
    const searchStartTime = Date.now();
    const searchResults = await userDB.searchUsers('用户1');
    const searchTime = Date.now() - searchStartTime;
    console.log(`搜索耗时: ${searchTime}ms，结果数量: ${searchResults.length}`);
    
    // 测试分页性能
    const pageStartTime = Date.now();
    const pageResults = await userDB.getUsersPaginated(1, 50);
    const pageTime = Date.now() - pageStartTime;
    console.log(`分页查询耗时: ${pageTime}ms，结果数量: ${pageResults.length}`);
    
    console.log('✅ 性能测试完成！');
    
  } catch (error) {
    console.error('❌ 性能测试失败:', error);
  }
}

/**
 * 运行所有测试
 */
export async function runAllTests() {
  console.log('🚀 开始运行 Dexie.js 数据库测试套件...\n');
  
  await testDatabaseOperations();
  await testErrorHandling();
  await testPerformance();
  
  console.log('\n🎉 所有测试完成！');
}

// 如果直接运行此文件，则执行测试
if (typeof window !== 'undefined') {
  // 浏览器环境
  window.runDexieTests = runAllTests;
  console.log('测试函数已挂载到 window.runDexieTests，可在控制台中调用');
}

export default {
  testDatabaseOperations,
  testErrorHandling,
  testPerformance,
  runAllTests
};
