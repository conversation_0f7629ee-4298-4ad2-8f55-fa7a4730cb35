<template>
  <div class="navigator-content">
    <div class="content-header">
      <h2 class="content-title">
        <el-icon><Document /></el-icon>
        Navigator Three 管理
      </h2>
      <div class="header-actions">
        <el-button type="primary" :icon="Plus">
          添加文档
        </el-button>
      </div>
    </div>

    <div class="content-body">
      <el-card class="feature-card">
        <template #header>
          <div class="card-header">
            <span>文档管理</span>
          </div>
        </template>
        <!-- <div class="feature-grid">
          <div class="feature-item">
            <el-icon class="feature-icon"><FileText /></el-icon>
            <h3>用户手册</h3>
            <p>系统使用说明和操作指南</p>
          </div>
          <div class="feature-item">
            <el-icon class="feature-icon"><BookOpen /></el-icon>
            <h3>API文档</h3>
            <p>接口文档和开发指南</p>
          </div>
          <div class="feature-item">
            <el-icon class="feature-icon"><HelpCircle /></el-icon>
            <h3>常见问题</h3>
            <p>FAQ和问题解答</p>
          </div>
          <div class="feature-item">
            <el-icon class="feature-icon"><Settings /></el-icon>
            <h3>配置说明</h3>
            <p>系统配置和参数说明</p>
          </div>
        </div> -->
      </el-card>

      <el-card class="stats-card">
        <template #header>
          <div class="card-header">
            <span>文档统计</span>
          </div>
        </template>
        <div class="stats-grid">
          <div class="stat-item">
            <div class="stat-number">156</div>
            <div class="stat-label">总文档数</div>
          </div>
          <div class="stat-item">
            <div class="stat-number">23</div>
            <div class="stat-label">本月新增</div>
          </div>
          <div class="stat-item">
            <div class="stat-number">89%</div>
            <div class="stat-label">完成度</div>
          </div>
          <div class="stat-item">
            <div class="stat-number">4.8</div>
            <div class="stat-label">平均评分</div>
          </div>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script setup>
import { ref } from "vue";
// import { Plus, Document, FileText, BookOpen, HelpCircle, Settings } from "lucide-vue-next";

// 这里可以添加具体的业务逻辑
const loading = ref(false);

// 示例方法
const handleAddNew = () => {
  console.log("添加新文档");
};
</script>

<style scoped>
.navigator-content {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.content-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 0 20px;
}

.content-title {
  color: white;
  font-size: 24px;
  font-weight: bold;
  display: flex;
  align-items: center;
  gap: 10px;
  margin: 0;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.content-body {
  flex: 1;
  padding: 0 20px;
  overflow-y: auto;
}

.feature-card,
.stats-card {
  margin-bottom: 20px;
  background: rgba(255, 255, 255, 0.05) !important;
  border: 1px solid rgba(255, 255, 255, 0.1) !important;
}

:deep(.el-card__header) {
  background: rgba(255, 255, 255, 0.1) !important;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1) !important;
}

:deep(.el-card__body) {
  background: transparent !important;
}

.card-header {
  color: white;
  font-weight: bold;
  font-size: 16px;
}

.feature-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
}

.feature-item {
  text-align: center;
  padding: 20px;
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.02);
  border: 1px solid rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
  cursor: pointer;
}

.feature-item:hover {
  background: rgba(100, 255, 218, 0.1);
  border-color: rgba(100, 255, 218, 0.3);
  transform: translateY(-2px);
}

.feature-icon {
  font-size: 32px;
  color: #64ffda;
  margin-bottom: 10px;
}

.feature-item h3 {
  color: white;
  margin: 10px 0;
  font-size: 18px;
}

.feature-item p {
  color: rgba(255, 255, 255, 0.7);
  margin: 0;
  font-size: 14px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
}

.stat-item {
  text-align: center;
  padding: 20px;
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.02);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.stat-number {
  font-size: 32px;
  font-weight: bold;
  color: #64ffda;
  margin-bottom: 5px;
}

.stat-label {
  color: rgba(255, 255, 255, 0.8);
  font-size: 14px;
}

/* Responsive design */
@media (max-width: 768px) {
  .content-header {
    flex-direction: column;
    align-items: stretch;
    gap: 15px;
  }

  .feature-grid,
  .stats-grid {
    grid-template-columns: 1fr;
  }
}
</style>
