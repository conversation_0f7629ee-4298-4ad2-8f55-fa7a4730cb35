<template>
  <!-- Password Vault Modal -->
  <Transition name="modal-fade">
    <div v-if="show" class="password-vault-overlay" @click.self="$emit('close')">
      <Transition name="vault-bounce">
        <div v-if="show" class="password-vault-container">
          <!-- Vault particles -->
          <div class="vault-particles">
            <div v-for="i in 20" :key="i" class="vault-particle"></div>
          </div>
          <!-- Password verification screen -->
          <div>

            <button @click="$emit('close')" class="vault-close-btn">
              <X class="vault-icon" />
            </button>
          </div>
          
        </div>
      </Transition>
    </div>
  </Transition>
</template>

<script setup>
import { ref, computed, watch } from 'vue';
import {
  
} from 'lucide-vue-next';

// Props
const props = defineProps({
  show: {
    type: Boolean,
    default: false
  }
});

// Emits
const emit = defineEmits(['close']);


</script>

<style scoped>
/* Modal transitions */
.modal-fade-enter-active, .modal-fade-leave-active {
  transition: opacity 0.3s ease;
}

.modal-fade-enter-from, .modal-fade-leave-to {
  opacity: 0;
}

.vault-bounce-enter-active {
  animation: vault-bounce-in 0.5s ease-out;
}

.vault-bounce-leave-active {
  animation: vault-bounce-out 0.3s ease-in;
}
/* Password Vault Modal Styles */
.password-vault-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(10px);
  z-index: 120;
  display: flex;
  align-items: center;
  justify-content: center;
}

.password-vault-container {
  width: 95%;
  max-width: 1400px;
  height: 90vh;
  background: linear-gradient(135deg, #0f0c29, #302b63, #24243e);
  border-radius: 24px;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.5);
  overflow: hidden;
  display: flex;
  flex-direction: column;
  border: 1px solid rgba(255, 255, 255, 0.1);
  position: relative;
}

.vault-close-btn {
  position: absolute;
  top: 20px;
  right: 20px;
  background: rgba(255, 107, 107, 0.2);
  border: none;
  border-radius: 12px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  z-index: 20;
  padding: 0 12px;
}

.vault-close-btn:hover {
  background: rgba(255, 107, 107, 0.4);
  transform: translateY(-2px);
}

.vault-icon {
  width: 20px;
  height: 20px;
  color: white;
}

</style>