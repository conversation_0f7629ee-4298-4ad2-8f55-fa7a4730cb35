<!-- BookmarkImporter.vue -->
<template>
  <div class="bookmark-container">
    <!-- 上传区域 -->
    <div class="upload-area" v-if="!folders.length">
      <div class="upload-box" @dragover.prevent @drop.prevent="handleDrop">
        <input
          type="file"
          ref="fileInput"
          @change="handleFileSelect"
          accept=".html,.htm"
          style="display: none"
        />
        <div class="upload-content" @click="triggerFileInput">
          <div class="upload-icon">
            <svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
              <polyline points="7 10 12 15 17 10"></polyline>
              <line x1="12" y1="15" x2="12" y2="3"></line>
            </svg>
          </div>
          <h3>导入书签文件</h3>
          <p>支持拖拽或点击选择 .html 书签文件</p>
        </div>
      </div>
    </div>

    <!-- 书签展示区域 -->
    <div class="bookmarks-layout" v-else>
      <!-- 搜索栏 -->
      <div class="search-bar">
        <div class="search-input-wrapper">
          <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="search-icon">
            <circle cx="11" cy="11" r="8"></circle>
            <line x1="21" y1="21" x2="16.65" y2="16.65"></line>
          </svg>
          <input
            type="text"
            v-model="searchQuery"
            placeholder="搜索书签名称或网址..."
            class="search-input"
            @keyup.enter="performSearch"
          />
          <button v-if="searchQuery" class="clear-btn" @click="clearSearch">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <line x1="18" y1="6" x2="6" y2="18"></line>
              <line x1="6" y1="6" x2="18" y2="18"></line>
            </svg>
          </button>
        </div>
        
        <button class="btn-clear" @click="clearBookmarks">
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <polyline points="3 6 5 6 21 6"></polyline>
            <path d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2"></path>
            <line x1="10" y1="11" x2="10" y2="17"></line>
            <line x1="14" y1="11" x2="14" y2="17"></line>
          </svg>
          清除
        </button>
      </div>

      <!-- 统计信息 -->
      <div class="stats-bar">
        <span class="stats-text">
          <strong>{{ totalBookmarks }}</strong> 个书签 · 
          <strong>{{ folders.length }}</strong> 个分类
        </span>
        <span v-if="searchQuery" class="search-stats">
          找到 <strong>{{ filteredBookmarks.length }}</strong> 个结果
        </span>
      </div>

      <!-- 分类书签列表 -->
      <div class="folders-container">
        <div 
          v-for="(folder, folderIndex) in displayedFolders" 
          :key="folderIndex"
          class="folder-section"
        >
          <!-- 分类标题 -->
          <div class="folder-header">
            <div class="folder-title-wrapper">
              <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="folder-icon">
                <path d="M22 19a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h5l2 3h9a2 2 0 0 1 2 2z"></path>
              </svg>
              <h3 class="folder-title">{{ folder.name }}</h3>
            </div>
            <span class="bookmark-count">{{ folder.bookmarks.length }} 个书签</span>
          </div>

          <!-- 书签网格 -->
          <div class="bookmarks-grid">
            <div 
              v-for="(bookmark, bookmarkIndex) in folder.bookmarks" 
              :key="bookmarkIndex"
              class="bookmark-card"
              @click="handleBookmarkClick(bookmark)"
              :title="`${bookmark.title}\n${bookmark.url}`"
            >
              <div class="card-content">
                <!-- 图标区域 -->
                <div class="icon-wrapper" :class="{ 'no-icon': !bookmark.favicon }">
                  <img 
                    v-if="bookmark.ICON" 
                    :src="bookmark.ICON" 
                    :alt="bookmark.title"
                    @error="useDefaultIcon"
                    class="favicon"
                    loading="lazy"
                  />
                  <div v-else class="default-icon">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                      <circle cx="12" cy="12" r="10"></circle>
                      <line x1="2" y1="12" x2="22" y2="12"></line>
                      <path d="M12 2a15.3 15.3 0 0 1 4 10 15.3 15.3 0 0 1-4 10 15.3 15.3 0 0 1-4-10 15.3 15.3 0 0 1 4-10z"></path>
                    </svg>
                  </div>
                </div>

                <!-- 文本内容 -->
                <div class="text-content">
                  <h4 class="bookmark-title">{{ bookmark.title }}</h4>
                  <p class="bookmark-url">{{ bookmark.hostname }}</p>
                </div>
              </div>
            </div>

            <!-- 空状态 -->
            <div v-if="folder.bookmarks.length === 0" class="empty-state">
              <svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M22 19a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h5l2 3h9a2 2 0 0 1 2 2z"></path>
                <line x1="9" y1="9" x2="15" y2="9"></line>
                <line x1="9" y1="14" x2="15" y2="14"></line>
                <line x1="9" y1="19" x2="15" y2="19"></line>
              </svg>
              <p>该分类暂无书签</p>
            </div>
          </div>
        </div>

        <!-- 无搜索结果 -->
        <div v-if="searchQuery && displayedFolders.length === 0" class="no-results">
          <svg xmlns="http://www.w3.org/2000/svg" width="64" height="64" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <circle cx="12" cy="12" r="10"></circle>
            <line x1="12" y1="8" x2="12" y2="12"></line>
            <line x1="12" y1="16" x2="12.01" y2="16"></line>
          </svg>
          <h3>未找到匹配的书签</h3>
          <p>尝试调整搜索关键词或查看其他分类</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, reactive } from 'vue'

// 响应式数据
const fileInput = ref(null)
const searchQuery = ref('')
const folders = ref([])

// 安全解析 URL 主机名
const parseHostname = (url) => {
  try {
    // 确保 URL 有协议前缀
    const urlWithProtocol = url.startsWith('http://') || url.startsWith('https://') ? url : `https://${url}`
    const urlObj = new URL(urlWithProtocol)
    return urlObj.hostname.replace(/^www\./, '') // 移除 www 前缀
  } catch (error) {
    console.warn('无法解析 URL:', url, error)
    // 返回原始 URL 或简化版本
    return url.split('/')[0].replace(/^www\./, '') || url
  }
}

// 解析 HTML 书签文件
const parseBookmarks = (htmlContent) => {
  const parser = new DOMParser()
  const doc = parser.parseFromString(htmlContent, 'text/html')
  
  // 存储所有文件夹和书签
  const folderMap = new Map()
  
  // 查找所有 DT 元素（包含文件夹和书签）
  const dtElements = doc.querySelectorAll('dt')
  
  dtElements.forEach(dt => {
    const folderHeader = dt.querySelector('h3')
    const link = dt.querySelector('a')
    
    if (folderHeader) {
      // 这是一个文件夹
      const folderName = folderHeader.textContent.trim() || '未命名分类'
      if (!folderMap.has(folderName)) {
        folderMap.set(folderName, {
          name: folderName,
          bookmarks: []
        })
      }
    } else if (link) {
      // 这是一个书签链接
      const url = link.getAttribute('href')
      if (url) {
        // 查找最近的父级文件夹
        let parentFolder = '其他书签'
        let current = dt.parentElement
        
        while (current && current !== doc) {
          const header = current.querySelector('h3')
          if (header) {
            parentFolder = header.textContent.trim() || '其他书签'
            break
          }
          current = current.parentElement
        }
        
        // 如果文件夹不存在，创建它
        if (!folderMap.has(parentFolder)) {
          folderMap.set(parentFolder, {
            name: parentFolder,
            bookmarks: []
          })
        }
        
        // 添加书签到对应文件夹
        const hostname = parseHostname(url)
        folderMap.get(parentFolder).bookmarks.push({
          title: link.textContent.trim() || '无标题',
          url: url,
          hostname: hostname,
          favicon: `https://www.google.com/s2/favicons?domain=${url}&sz=64`,
          type: 'link'
        })
      }
    }
  })
  
  // 转换为数组并排序（将"其他书签"放到最后）
  const folderArray = Array.from(folderMap.values())
  
  // 特殊排序：常用分类优先
  const priorityFolders = ['常用', '收藏', '工作', '学习', '开发', '工具', '社交', '娱乐']
  const otherFolders = []
  const prioritizedFolders = []
  
  folderArray.forEach(folder => {
    const folderNameLower = folder.name.toLowerCase()
    if (priorityFolders.some(pf => folderNameLower.includes(pf.toLowerCase()))) {
      prioritizedFolders.push(folder)
    } else {
      otherFolders.push(folder)
    }
  })
  
  // 按名称排序
  prioritizedFolders.sort((a, b) => a.name.localeCompare(b.name))
  otherFolders.sort((a, b) => a.name.localeCompare(b.name))
  
  // 合并结果（优先级高的在前，其他按字母顺序）
  return [...prioritizedFolders, ...otherFolders]
}

// 处理文件选择
const handleFileSelect = (event) => {
  const file = event.target.files[0]
  if (!file) return
  
  if (!file.name.endsWith('.html') && !file.name.endsWith('.htm')) {
    alert('请上传 .html 或 .htm 格式的书签文件')
    return
  }

  const reader = new FileReader()
  reader.onload = (e) => {
    try {
      const content = e.target.result
      const parsedFolders = parseBookmarks(content)
      folders.value = parsedFolders
    } catch (error) {
      console.error('解析书签文件失败:', error)
      alert('无法解析书签文件，请检查文件格式')
    }
  }
  reader.readAsText(file)
}

// 处理拖拽上传
const handleDrop = (event) => {
  const file = event.dataTransfer.files[0]
  if (file && (file.name.endsWith('.html') || file.name.endsWith('.htm'))) {
    const reader = new FileReader()
    reader.onload = (e) => {
      try {
        const content = e.target.result
        const parsedFolders = parseBookmarks(content)
        folders.value = parsedFolders
      } catch (error) {
        console.error('解析书签文件失败:', error)
        alert('无法解析书签文件，请检查文件格式')
      }
    }
    reader.readAsText(file)
  }
}

// 触发文件选择对话框
const triggerFileInput = () => {
  fileInput.value.click()
}

// 处理书签点击
const handleBookmarkClick = (bookmark) => {
  if (bookmark.url) {
    window.open(bookmark.url, '_blank', 'noopener,noreferrer')
  }
}

// 清除所有书签
const clearBookmarks = () => {
  folders.value = []
  searchQuery.value = ''
}

// 清除搜索
const clearSearch = () => {
  searchQuery.value = ''
}

// 执行搜索
const performSearch = () => {
  // 搜索在计算属性中处理
}

// 计算属性：过滤后的书签
const filteredBookmarks = computed(() => {
  if (!searchQuery.value.trim()) {
    return []
  }
  
  const query = searchQuery.value.trim().toLowerCase()
  const results = []
  
  folders.value.forEach(folder => {
    const matchingBookmarks = folder.bookmarks.filter(bookmark => 
      bookmark.title.toLowerCase().includes(query) || 
      bookmark.hostname.toLowerCase().includes(query)
    )
    
    if (matchingBookmarks.length > 0) {
      results.push({
        name: folder.name,
        bookmarks: matchingBookmarks
      })
    }
  })
  
  return results
})

// 计算属性：显示的文件夹列表
const displayedFolders = computed(() => {
  if (searchQuery.value.trim()) {
    return filteredBookmarks.value
  }
  return folders.value
})

// 计算属性：书签总数
const totalBookmarks = computed(() => {
  return folders.value.reduce((total, folder) => total + folder.bookmarks.length, 0)
})

// 图标加载失败时使用默认图标
const useDefaultIcon = (event) => {
  const img = event.target
  img.style.display = 'none' // 隐藏损坏的图片
  // 默认图标会自动显示
}
</script>

<style scoped>
.bookmark-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  font-family: 'Segoe UI', -apple-system, BlinkMacSystemFont, 'Roboto', 'Helvetica Neue', sans-serif;
  background: linear-gradient(135deg, #f5f7fa 0%, #e4edf5 100%);
  min-height: 100vh;
}

/* 上传区域样式 */
.upload-area {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 60vh;
  padding: 40px 20px;
}

.upload-box {
  border: 2px dashed #d1d8e0;
  border-radius: 16px;
  padding: 60px 40px;
  text-align: center;
  background: white;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.05);
  backdrop-filter: blur(4px);
  border: 1px solid rgba(255, 255, 255, 0.18);
  border-radius: 10px;
  border: 1px solid rgba(255, 255, 255, 0.18);
}

.upload-box:hover {
  border-color: #6b72e8;
  transform: translateY(-4px);
  box-shadow: 0 12px 48px rgba(107, 114, 232, 0.2);
}

.upload-content {
  color: #4b5563;
}

.upload-icon {
  margin: 0 auto 24px;
  width: 72px;
  height: 72px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #6b72e8, #8b5cf6);
  border-radius: 50%;
  color: white;
  box-shadow: 0 8px 24px rgba(107, 114, 232, 0.4);
}

.upload-icon svg {
  transform: scale(1.3);
}

.upload-content h3 {
  margin: 0 0 12px;
  color: #1f2937;
  font-size: 24px;
  font-weight: 600;
  line-height: 1.3;
}

.upload-content p {
  margin: 0;
  color: #6b7280;
  font-size: 16px;
  line-height: 1.5;
}

/* 书签布局样式 */
.bookmarks-layout {
  background: white;
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.08);
  backdrop-filter: blur(4px);
  border: 1px solid rgba(255, 255, 255, 0.18);
}

/* 搜索栏样式 */
.search-bar {
  display: flex;
  align-items: center;
  padding: 20px 24px;
  gap: 16px;
  background: #f8fafc;
  border-bottom: 1px solid #e2e8f0;
}

.search-input-wrapper {
  flex: 1;
  position: relative;
  max-width: 500px;
}

.search-icon {
  position: absolute;
  left: 14px;
  top: 50%;
  transform: translateY(-50%);
  color: #94a3b8;
  pointer-events: none;
}

.search-input {
  width: 100%;
  padding: 12px 16px 12px 44px;
  border: 2px solid #e2e8f0;
  border-radius: 12px;
  font-size: 15px;
  color: #1e293b;
  background: white;
  transition: all 0.2s ease;
  outline: none;
}

.search-input:focus {
  border-color: #6b72e8;
  box-shadow: 0 0 0 3px rgba(107, 114, 232, 0.1);
}

.clear-btn {
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  cursor: pointer;
  color: #94a3b8;
  padding: 4px;
  border-radius: 6px;
  transition: all 0.2s ease;
}

.clear-btn:hover {
  background: #f1f5f9;
  color: #64748b;
}

.btn-clear {
  background: #ef4444;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 12px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 6px;
  transition: all 0.2s ease;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.btn-clear:hover {
  background: #dc2626;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.15);
}

.btn-clear svg {
  width: 14px;
  height: 14px;
}

/* 统计栏样式 */
.stats-bar {
  padding: 16px 24px;
  background: #f8fafc;
  border-bottom: 1px solid #e2e8f0;
  font-size: 14px;
  color: #64748b;
}

.stats-text {
  font-weight: 500;
}

.search-stats {
  margin-left: 16px;
  color: #059669;
  font-weight: 500;
}

/* 分类容器 */
.folders-container {
  padding: 24px;
  max-height: calc(100vh - 200px);
  overflow-y: auto;
}

/* 分类标题样式 */
.folder-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 2px solid #f1f5f9;
}

.folder-title-wrapper {
  display: flex;
  align-items: center;
  gap: 10px;
}

.folder-icon {
  color: #6b72e8;
  flex-shrink: 0;
}

.folder-title {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #1e293b;
  line-height: 1.4;
}

.bookmark-count {
  font-size: 14px;
  color: #64748b;
  font-weight: 500;
  background: #f1f5f9;
  padding: 6px 12px;
  border-radius: 20px;
  min-width: 80px;
  text-align: center;
}

/* 书签网格样式 */
.bookmarks-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
  gap: 16px;
  padding: 4px 0;
}

.bookmark-card {
  background: white;
  border: 2px solid #e2e8f0;
  border-radius: 16px;
  padding: 16px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  position: relative;
  overflow: hidden;
  height: 100px;
}

.bookmark-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);
  border-color: #6b72e8;
}

.card-content {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  height: 100%;
}

/* 图标样式 */
.icon-wrapper {
  flex-shrink: 0;
  width: 40px;
  height: 40px;
  border-radius: 12px;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f8fafc;
  border: 2px solid #e2e8f0;
  transition: all 0.2s ease;
}

.bookmark-card:hover .icon-wrapper {
  background: #6b72e8;
  border-color: #6b72e8;
}

.icon-wrapper.no-icon {
  background: #f1f5f9;
  border-color: #e2e8f0;
}

.favicon {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: opacity 0.2s ease;
}

.default-icon {
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.default-icon svg {
  width: 100%;
  height: 100%;
  stroke: #64748b;
  transition: all 0.2s ease;
}

.bookmark-card:hover .default-icon svg {
  stroke: white;
}

/* 文本内容样式 */
.text-content {
  flex: 1;
  min-width: 0;
}

.bookmark-title {
  margin: 0 0 4px;
  font-size: 15px;
  font-weight: 600;
  color: #1e293b;
  line-height: 1.4;
  max-height: 2.8em;
  overflow: hidden;
  text-overflow: ellipsis;
}

.bookmark-url {
  margin: 0;
  font-size: 13px;
  color: #64748b;
  font-weight: 500;
  max-height: 1.8em;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 空状态样式 */
.empty-state, .no-results {
  grid-column: 1 / -1;
  text-align: center;
  padding: 40px 20px;
  color: #94a3b8;
}

.empty-state svg, .no-results svg {
  margin: 0 auto 16px;
  opacity: 0.6;
}

.empty-state p, .no-results p {
  margin: 0;
  font-size: 14px;
}

.no-results h3 {
  margin: 0 0 8px;
  font-size: 18px;
  color: #1e293b;
  font-weight: 600;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .bookmark-container {
    padding: 16px;
  }
  
  .upload-box {
    padding: 40px 20px;
  }
  
  .upload-content h3 {
    font-size: 20px;
  }
  
  .upload-content p {
    font-size: 14px;
  }
  
  .search-bar {
    flex-direction: column;
    gap: 12px;
    padding: 16px;
  }
  
  .search-input-wrapper {
    width: 100%;
  }
  
  .btn-clear {
    width: 100%;
    justify-content: center;
  }
  
  .stats-bar {
    padding: 12px 16px;
    font-size: 13px;
  }
  
  .folders-container {
    padding: 16px;
  }
  
  .folder-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }
  
  .bookmark-count {
    margin-left: 30px;
  }
  
  .bookmarks-grid {
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 12px;
  }
  
  .bookmark-card {
    padding: 12px;
    height: 90px;
  }
  
  .icon-wrapper {
    width: 36px;
    height: 36px;
  }
  
  .bookmark-title {
    font-size: 14px;
  }
  
  .bookmark-url {
    font-size: 12px;
  }
}

@media (max-width: 480px) {
  .bookmarks-grid {
    grid-template-columns: 1fr;
  }
  
  .bookmark-count {
    margin-left: 0;
  }
}
</style>