# 菜谱管理系统演示说明

## 演示步骤

### 1. 启动应用
1. 打开浏览器访问：http://localhost:5174/
2. 你会看到一个漂亮的桌面界面

### 2. 进入管理端
1. 点击底部dock中的"管理端"图标（用户图标）
2. 在弹出的对话框中输入密钥：`zhangzuying`
3. 点击确认进入管理端界面

### 3. 打开菜谱管理
1. 在管理端界面中，点击"美食MALL"模块
2. 这将打开菜谱管理界面（AFoodMall.vue）

### 4. 查看现有菜谱
- 界面会自动加载示例菜谱数据
- 你可以看到包含以下信息的表格：
  - ID、菜名、描述、分类、难度、制作时间、评分、收藏状态

### 5. 搜索功能测试
1. 在搜索框中输入"蜜汁"
2. 表格会自动过滤显示相关菜谱
3. 清空搜索框可以显示所有菜谱

### 6. 添加新菜谱
1. 点击"添加菜谱"按钮
2. 填写表单：
   - **菜名**：红烧肉
   - **分类**：家常菜
   - **描述**：经典的家常红烧肉
   - **制作时间**：60分钟
   - **难度**：中等
   - **份量**：4人份
   - **评分**：5星
   - **标签**：家常,下饭,经典
   - **故事**：妈妈的拿手菜

3. 添加食材：
   - 五花肉 500g
   - 生抽 2勺
   - 老抽 1勺
   - 冰糖 适量

4. 添加制作步骤：
   - 步骤1：五花肉切块，焯水去腥
   - 步骤2：热锅下肉块煸炒出油
   - 步骤3：加入调料炖煮40分钟

5. 点击"保存菜谱"

### 7. 编辑菜谱
1. 在表格中找到刚添加的菜谱
2. 点击编辑按钮（铅笔图标）
3. 修改任意信息，比如将评分改为4星
4. 点击"更新菜谱"

### 8. 图片上传测试
1. 在编辑或添加菜谱时
2. 点击主图片上传区域
3. 选择一张图片文件（小于2MB）
4. 图片会自动转换为base64格式并显示预览

### 9. 删除菜谱
1. 选择一个菜谱
2. 点击删除按钮（垃圾桶图标）
3. 在确认对话框中点击"确定"

### 10. 查看用户端界面
1. 关闭管理界面
2. 点击dock中的"美食MALL"图标
3. 这将打开用户端界面（FoodMall.vue）
4. 你可以看到刚才管理的菜谱数据在用户端的展示效果

## 功能亮点

### 1. 数据持久化
- 所有数据保存在浏览器的IndexedDB中
- 刷新页面数据不会丢失
- 支持离线使用

### 2. 图片处理
- 自动将图片转换为base64格式
- 支持图片预览
- 文件大小和格式验证

### 3. 动态表单
- 可以动态添加/删除食材
- 可以动态添加/删除制作步骤
- 每个步骤都可以上传图片

### 4. 搜索功能
- 支持多字段搜索
- 实时搜索结果
- 模糊匹配

### 5. 数据验证
- 前端表单验证
- 必填字段检查
- 数据格式验证

### 6. 用户体验
- 响应式设计
- 加载状态提示
- 操作成功/失败反馈
- 确认对话框防误操作

## 技术特点

1. **Vue 3 + Element Plus**：现代化的前端技术栈
2. **IndexedDB**：浏览器本地数据库
3. **Base64图片存储**：无需服务器的图片处理方案
4. **组件化设计**：可复用的组件架构
5. **响应式布局**：适配不同设备

## 测试建议

1. 尝试添加包含图片的完整菜谱
2. 测试搜索功能的各种关键词
3. 验证表单验证是否正常工作
4. 测试大量数据的分页功能
5. 检查数据在页面刷新后是否保持

## 注意事项

1. 首次使用会自动初始化示例数据
2. 图片建议控制在2MB以内
3. 管理端密钥是"zhangzuying"
4. 数据存储在浏览器本地，清除浏览器数据会丢失

## 扩展可能

这个系统为进一步扩展提供了良好的基础：
- 可以轻松添加更多字段
- 可以扩展为云端同步
- 可以添加更多的管理功能
- 可以集成到更大的系统中
