<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据序列化测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .result {
            margin-top: 10px;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 4px;
            white-space: pre-wrap;
        }
        .success {
            background: #d4edda;
            color: #155724;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
        }
    </style>
</head>
<body>
    <h1>数据序列化修复测试</h1>
    
    <div class="test-section">
        <h2>响应式对象序列化测试</h2>
        <button class="test-button" onclick="testReactiveObjectSerialization()">测试响应式对象序列化</button>
        <div id="reactive-result" class="result"></div>
    </div>
    
    <div class="test-section">
        <h2>菜谱数据结构测试</h2>
        <button class="test-button" onclick="testRecipeDataStructure()">测试菜谱数据结构</button>
        <div id="recipe-structure-result" class="result"></div>
    </div>
    
    <div class="test-section">
        <h2>IndexedDB兼容性测试</h2>
        <button class="test-button" onclick="testIndexedDBCompatibility()">测试IndexedDB兼容性</button>
        <div id="indexeddb-result" class="result"></div>
    </div>

    <script>
        // 模拟Vue的响应式对象
        function createReactiveObject(data) {
            return new Proxy(data, {
                get(target, prop) {
                    console.log(`Getting ${prop}`);
                    return target[prop];
                },
                set(target, prop, value) {
                    console.log(`Setting ${prop} to ${value}`);
                    target[prop] = value;
                    return true;
                }
            });
        }

        // 测试响应式对象序列化
        window.testReactiveObjectSerialization = function() {
            const resultDiv = document.getElementById('reactive-result');
            try {
                // 创建一个模拟的响应式对象
                const reactiveData = createReactiveObject({
                    name: '测试菜谱',
                    ingredients: [
                        { name: '食材1', amount: '100g' },
                        { name: '食材2', amount: '200ml' }
                    ],
                    steps: [
                        { description: '步骤1', tip: '小贴士1' },
                        { description: '步骤2', tip: '小贴士2' }
                    ]
                });

                // 尝试直接序列化（这可能会失败）
                let directSerialization;
                try {
                    directSerialization = JSON.stringify(reactiveData);
                } catch (e) {
                    directSerialization = '直接序列化失败: ' + e.message;
                }

                // 使用我们的修复方法
                const cleanData = JSON.parse(JSON.stringify(reactiveData));
                const fixedSerialization = JSON.stringify(cleanData);

                resultDiv.className = 'result success';
                resultDiv.textContent = `✓ 序列化测试完成！
                
直接序列化结果: ${directSerialization}

修复后序列化结果: ${fixedSerialization}

修复方法有效: ${fixedSerialization.length > 0 ? '是' : '否'}`;

            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = '✗ 序列化测试失败：' + error.message;
            }
        };

        // 测试菜谱数据结构
        window.testRecipeDataStructure = function() {
            const resultDiv = document.getElementById('recipe-structure-result');
            try {
                const recipeData = {
                    name: '测试菜谱',
                    description: '测试描述',
                    category: 'home',
                    image: 'data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD...',
                    images: ['data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD...'],
                    cookTime: 30,
                    difficulty: 'easy',
                    servings: 2,
                    rating: 5,
                    isFavorite: false,
                    spicy: '',
                    tags: ['测试', '简单'],
                    story: '测试故事',
                    ingredients: [
                        { name: '食材1', amount: '100g' },
                        { name: '食材2', amount: '200ml' }
                    ],
                    steps: [
                        { description: '步骤1', tip: '小贴士1', image: '' },
                        { description: '步骤2', tip: '小贴士2', image: '' }
                    ],
                    createdAt: new Date(),
                    updatedAt: new Date()
                };

                // 测试序列化
                const serialized = JSON.stringify(recipeData);
                const deserialized = JSON.parse(serialized);

                // 验证数据完整性
                const isValid = 
                    deserialized.name === recipeData.name &&
                    deserialized.ingredients.length === recipeData.ingredients.length &&
                    deserialized.steps.length === recipeData.steps.length &&
                    Array.isArray(deserialized.tags) &&
                    Array.isArray(deserialized.ingredients) &&
                    Array.isArray(deserialized.steps);

                resultDiv.className = 'result success';
                resultDiv.textContent = `✓ 菜谱数据结构测试完成！
                
原始数据大小: ${JSON.stringify(recipeData).length} 字符
序列化后大小: ${serialized.length} 字符
数据完整性: ${isValid ? '通过' : '失败'}
包含字段数: ${Object.keys(deserialized).length}

主要字段验证:
- 菜名: ${deserialized.name}
- 食材数量: ${deserialized.ingredients.length}
- 步骤数量: ${deserialized.steps.length}
- 标签数量: ${deserialized.tags.length}`;

            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = '✗ 菜谱数据结构测试失败：' + error.message;
            }
        };

        // 测试IndexedDB兼容性
        window.testIndexedDBCompatibility = function() {
            const resultDiv = document.getElementById('indexeddb-result');
            
            if (!window.indexedDB) {
                resultDiv.className = 'result error';
                resultDiv.textContent = '✗ 浏览器不支持IndexedDB';
                return;
            }

            try {
                const request = indexedDB.open('TestDB', 1);
                
                request.onerror = function() {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = '✗ IndexedDB连接失败';
                };

                request.onsuccess = function(event) {
                    const db = event.target.result;
                    
                    // 测试数据
                    const testData = {
                        id: 1,
                        name: '测试菜谱',
                        ingredients: [{ name: '食材1', amount: '100g' }],
                        steps: [{ description: '步骤1', tip: '小贴士' }],
                        tags: ['测试'],
                        createdAt: new Date()
                    };

                    // 清理数据以确保兼容性
                    const cleanData = JSON.parse(JSON.stringify(testData));

                    resultDiv.className = 'result success';
                    resultDiv.textContent = `✓ IndexedDB兼容性测试通过！
                    
浏览器: ${navigator.userAgent.split(' ')[0]}
IndexedDB版本: 支持
数据清理: 成功
测试数据大小: ${JSON.stringify(cleanData).length} 字符

数据清理前后对比:
- 原始对象类型: ${typeof testData.createdAt}
- 清理后类型: ${typeof cleanData.createdAt}
- 数据完整性: ${cleanData.name === testData.name ? '保持' : '丢失'}`;

                    db.close();
                };

                request.onupgradeneeded = function(event) {
                    const db = event.target.result;
                    if (!db.objectStoreNames.contains('test')) {
                        db.createObjectStore('test', { keyPath: 'id' });
                    }
                };

            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = '✗ IndexedDB兼容性测试失败：' + error.message;
            }
        };
    </script>
</body>
</html>
