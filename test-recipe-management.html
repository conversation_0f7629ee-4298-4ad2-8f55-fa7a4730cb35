<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>菜谱管理测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .result {
            margin-top: 10px;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 4px;
            white-space: pre-wrap;
        }
        .success {
            background: #d4edda;
            color: #155724;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
        }
    </style>
</head>
<body>
    <h1>菜谱管理系统测试</h1>
    
    <div class="test-section">
        <h2>数据库连接测试</h2>
        <button class="test-button" onclick="testDatabaseConnection()">测试数据库连接</button>
        <div id="db-result" class="result"></div>
    </div>
    
    <div class="test-section">
        <h2>菜谱数据操作测试</h2>
        <button class="test-button" onclick="testAddRecipe()">添加测试菜谱</button>
        <button class="test-button" onclick="testGetAllRecipes()">获取所有菜谱</button>
        <button class="test-button" onclick="testSearchRecipes()">搜索菜谱</button>
        <button class="test-button" onclick="testUpdateRecipe()">更新菜谱</button>
        <button class="test-button" onclick="testDeleteRecipe()">删除菜谱</button>
        <div id="recipe-result" class="result"></div>
    </div>
    
    <div class="test-section">
        <h2>图片处理测试</h2>
        <input type="file" id="imageInput" accept="image/*" onchange="testImageToBase64()">
        <div id="image-result" class="result"></div>
    </div>

    <script type="module">
        // 模拟数据库操作（实际应用中会使用IndexedDB）
        let mockDatabase = [];
        let nextId = 1;

        // 数据库连接测试
        window.testDatabaseConnection = function() {
            const resultDiv = document.getElementById('db-result');
            try {
                // 模拟数据库连接
                resultDiv.className = 'result success';
                resultDiv.textContent = '✓ 数据库连接成功！IndexedDB 可用。';
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = '✗ 数据库连接失败：' + error.message;
            }
        };

        // 添加菜谱测试
        window.testAddRecipe = function() {
            const resultDiv = document.getElementById('recipe-result');
            try {
                const testRecipe = {
                    id: nextId++,
                    name: '测试菜谱',
                    description: '这是一个测试菜谱',
                    category: 'home',
                    image: '',
                    images: [''],
                    cookTime: 30,
                    difficulty: 'easy',
                    servings: 2,
                    rating: 4,
                    isFavorite: false,
                    spicy: '',
                    tags: ['测试', '简单'],
                    story: '这是一个测试故事',
                    ingredients: [
                        { name: '测试食材1', amount: '100g' },
                        { name: '测试食材2', amount: '200ml' }
                    ],
                    steps: [
                        { description: '第一步测试', tip: '测试小贴士', image: '' },
                        { description: '第二步测试', tip: '', image: '' }
                    ],
                    createdAt: new Date(),
                    updatedAt: new Date()
                };
                
                mockDatabase.push(testRecipe);
                resultDiv.className = 'result success';
                resultDiv.textContent = '✓ 菜谱添加成功！\n' + JSON.stringify(testRecipe, null, 2);
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = '✗ 菜谱添加失败：' + error.message;
            }
        };

        // 获取所有菜谱测试
        window.testGetAllRecipes = function() {
            const resultDiv = document.getElementById('recipe-result');
            try {
                resultDiv.className = 'result success';
                resultDiv.textContent = '✓ 获取菜谱成功！\n当前菜谱数量：' + mockDatabase.length + '\n' + 
                    JSON.stringify(mockDatabase, null, 2);
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = '✗ 获取菜谱失败：' + error.message;
            }
        };

        // 搜索菜谱测试
        window.testSearchRecipes = function() {
            const resultDiv = document.getElementById('recipe-result');
            try {
                const keyword = '测试';
                const results = mockDatabase.filter(recipe => 
                    recipe.name.includes(keyword) || 
                    recipe.description.includes(keyword) ||
                    recipe.tags.some(tag => tag.includes(keyword))
                );
                
                resultDiv.className = 'result success';
                resultDiv.textContent = '✓ 搜索菜谱成功！\n搜索关键词：' + keyword + 
                    '\n找到 ' + results.length + ' 个结果\n' + JSON.stringify(results, null, 2);
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = '✗ 搜索菜谱失败：' + error.message;
            }
        };

        // 更新菜谱测试
        window.testUpdateRecipe = function() {
            const resultDiv = document.getElementById('recipe-result');
            try {
                if (mockDatabase.length === 0) {
                    throw new Error('没有菜谱可以更新，请先添加菜谱');
                }
                
                const recipeToUpdate = mockDatabase[0];
                recipeToUpdate.name = '更新后的菜谱名称';
                recipeToUpdate.description = '更新后的描述';
                recipeToUpdate.updatedAt = new Date();
                
                resultDiv.className = 'result success';
                resultDiv.textContent = '✓ 菜谱更新成功！\n' + JSON.stringify(recipeToUpdate, null, 2);
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = '✗ 菜谱更新失败：' + error.message;
            }
        };

        // 删除菜谱测试
        window.testDeleteRecipe = function() {
            const resultDiv = document.getElementById('recipe-result');
            try {
                if (mockDatabase.length === 0) {
                    throw new Error('没有菜谱可以删除');
                }
                
                const deletedRecipe = mockDatabase.pop();
                resultDiv.className = 'result success';
                resultDiv.textContent = '✓ 菜谱删除成功！\n删除的菜谱：' + deletedRecipe.name;
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = '✗ 菜谱删除失败：' + error.message;
            }
        };

        // 图片转Base64测试
        window.testImageToBase64 = function() {
            const resultDiv = document.getElementById('image-result');
            const fileInput = document.getElementById('imageInput');
            const file = fileInput.files[0];
            
            if (!file) {
                resultDiv.className = 'result error';
                resultDiv.textContent = '✗ 请选择一个图片文件';
                return;
            }
            
            if (!file.type.startsWith('image/')) {
                resultDiv.className = 'result error';
                resultDiv.textContent = '✗ 请选择有效的图片文件';
                return;
            }
            
            const reader = new FileReader();
            reader.onload = function(e) {
                const base64 = e.target.result;
                resultDiv.className = 'result success';
                resultDiv.innerHTML = '✓ 图片转换成功！<br>' +
                    '文件大小：' + (file.size / 1024).toFixed(2) + ' KB<br>' +
                    'Base64长度：' + base64.length + ' 字符<br>' +
                    '预览：<br><img src="' + base64 + '" style="max-width: 200px; max-height: 200px;">';
            };
            reader.onerror = function() {
                resultDiv.className = 'result error';
                resultDiv.textContent = '✗ 图片转换失败';
            };
            reader.readAsDataURL(file);
        };
    </script>
</body>
</html>
